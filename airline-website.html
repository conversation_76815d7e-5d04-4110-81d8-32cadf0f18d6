<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航空公司官网</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
        }

        /* Header */
        .header {
            background: #8B4513;
            color: white;
            padding: 1rem 0;
            position: relative;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            background: #DC143C;
            padding: 0.5rem 1rem;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: #DC143C;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect fill="%23e0e0e0" width="1200" height="600"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="%23999" font-size="40">飞机与雪山背景</text></svg>');
            background-size: cover;
            background-position: center;
            height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }

        .hero-content h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }

        /* Booking Form */
        .booking-section {
            background: white;
            padding: 2rem 0;
            box-shadow: 0 -5px 15px rgba(0,0,0,0.1);
        }

        .booking-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .booking-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 1px solid #ddd;
        }

        .booking-tab {
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .booking-tab.active {
            border-bottom-color: #DC143C;
            color: #DC143C;
        }

        .booking-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .form-group input,
        .form-group select {
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .search-btn {
            background: #DC143C;
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }

        .search-btn:hover {
            background: #B91C3C;
        }

        /* Product Zone */
        .product-zone {
            padding: 4rem 0;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            font-size: 2rem;
            margin-bottom: 3rem;
            text-align: center;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-image {
            height: 200px;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .product-content {
            padding: 1.5rem;
        }

        .product-title {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .product-description {
            color: #666;
            margin-bottom: 1rem;
        }

        .product-link {
            color: #DC143C;
            text-decoration: none;
            font-weight: bold;
        }

        /* Travel Recommendations */
        .travel-recommendations {
            padding: 4rem 0;
        }

        .recommendation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .recommendation-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .recommendation-image {
            height: 180px;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .recommendation-content {
            padding: 1.5rem;
        }

        /* Footer */
        .footer {
            background: #333;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #DC143C;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-section ul li a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #555;
            margin-top: 2rem;
            padding-top: 1rem;
            text-align: center;
            color: #ccc;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: #8B4513;
                flex-direction: column;
                padding: 1rem;
                gap: 1rem;
            }

            .nav-menu.active {
                display: flex;
            }

            .mobile-menu-btn {
                display: block;
            }

            .hero-content h1 {
                font-size: 2rem;
            }

            .booking-form {
                grid-template-columns: 1fr;
            }

            .booking-tabs {
                flex-wrap: wrap;
            }

            .product-grid,
            .recommendation-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 375px) {
            .nav-container {
                padding: 0 1rem;
            }

            .hero-content h1 {
                font-size: 1.5rem;
            }

            .hero-content p {
                font-size: 1rem;
            }

            .booking-container,
            .container {
                padding: 0 1rem;
            }

            .booking-tab {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">航空公司</div>
            <nav>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="#home">首页</a></li>
                    <li><a href="#flights">航班</a></li>
                    <li><a href="#destinations">目的地</a></li>
                    <li><a href="#services">服务</a></li>
                    <li><a href="#about">关于我们</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
                <button class="mobile-menu-btn" id="mobileMenuBtn">☰</button>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1>探索世界，从这里开始</h1>
            <p>为您提供安全、舒适、便捷的航空服务</p>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking-section">
        <div class="booking-container">
            <div class="booking-tabs">
                <button class="booking-tab active">航班</button>
                <button class="booking-tab">酒店</button>
                <button class="booking-tab">租车</button>
                <button class="booking-tab">度假套餐</button>
            </div>
            <form class="booking-form">
                <div class="form-group">
                    <label for="from">出发地</label>
                    <input type="text" id="from" placeholder="请输入出发城市">
                </div>
                <div class="form-group">
                    <label for="to">目的地</label>
                    <input type="text" id="to" placeholder="请输入目的地城市">
                </div>
                <div class="form-group">
                    <label for="departure">出发日期</label>
                    <input type="date" id="departure">
                </div>
                <div class="form-group">
                    <label for="return">返程日期</label>
                    <input type="date" id="return">
                </div>
                <div class="form-group">
                    <label for="passengers">乘客数量</label>
                    <select id="passengers">
                        <option value="1">1位乘客</option>
                        <option value="2">2位乘客</option>
                        <option value="3">3位乘客</option>
                        <option value="4">4位乘客</option>
                    </select>
                </div>
                <button type="submit" class="search-btn">搜索航班</button>
            </form>
        </div>
    </section>

    <!-- Product Zone -->
    <section class="product-zone">
        <div class="container">
            <h2 class="section-title">产品专区</h2>
            <div class="product-grid">
                <div class="product-card">
                    <div class="product-image">Package Zone</div>
                    <div class="product-content">
                        <h3 class="product-title">套餐专区</h3>
                        <p class="product-description">精选旅行套餐，为您提供一站式旅行解决方案</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Student Area</div>
                    <div class="product-content">
                        <h3 class="product-title">学生专区</h3>
                        <p class="product-description">专为学生群体设计的优惠机票和服务</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Travel Recommendations -->
    <section class="travel-recommendations">
        <div class="container">
            <h2 class="section-title">旅行推荐</h2>
            <div class="recommendation-grid">
                <div class="recommendation-card">
                    <div class="recommendation-image">新加坡</div>
                    <div class="recommendation-content">
                        <h3>新加坡</h3>
                        <p>现代化都市与传统文化的完美融合</p>
                        <a href="#" class="product-link">查看详情 →</a>
                    </div>
                </div>
                <div class="recommendation-card">
                    <div class="recommendation-image">伦敦</div>
                    <div class="recommendation-content">
                        <h3>伦敦</h3>
                        <p>历史悠久的欧洲文化之都</p>
                        <a href="#" class="product-link">查看详情 →</a>
                    </div>
                </div>
                <div class="recommendation-card">
                    <div class="recommendation-image">首尔</div>
                    <div class="recommendation-content">
                        <h3>首尔</h3>
                        <p>时尚与传统并存的亚洲魅力之城</p>
                        <a href="#" class="product-link">查看详情 →</a>
                    </div>
                </div>
                <div class="recommendation-card">
                    <div class="recommendation-image">曼谷</div>
                    <div class="recommendation-content">
                        <h3>曼谷</h3>
                        <p>热带风情与佛教文化的神秘之地</p>
                        <a href="#" class="product-link">查看详情 →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Recommendations -->
    <section class="product-zone">
        <div class="container">
            <h2 class="section-title">产品推荐</h2>
            <div class="product-grid">
                <div class="product-card">
                    <div class="product-image">Sky Lounge</div>
                    <div class="product-content">
                        <h3 class="product-title">天空贵宾厅</h3>
                        <p class="product-description">享受专属贵宾服务，让您的旅程更加舒适</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Gourmet Meals</div>
                    <div class="product-content">
                        <h3 class="product-title">精选美食</h3>
                        <p class="product-description">空中美食体验，品味世界各地特色料理</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Love Shopping</div>
                    <div class="product-content">
                        <h3 class="product-title">爱购物</h3>
                        <p class="product-description">机上免税购物，精选商品等您选购</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Hotel Comfort</div>
                    <div class="product-content">
                        <h3 class="product-title">酒店舒适</h3>
                        <p class="product-description">精选合作酒店，为您提供舒适住宿体验</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Star Alliance -->
    <section class="travel-recommendations">
        <div class="container">
            <h2 class="section-title">明星联盟</h2>
            <div class="recommendation-card" style="max-width: 800px; margin: 0 auto;">
                <div class="recommendation-image" style="height: 300px;">Star Alliance - 无限旅程的开始</div>
                <div class="recommendation-content">
                    <h3>明星联盟为您开启无限旅程</h3>
                    <p>作为明星联盟成员，我们与全球顶级航空公司合作，为您提供覆盖全球的航线网络和一致的高品质服务体验。无论您前往世界何处，都能享受到统一标准的优质服务。</p>
                    <a href="#" class="product-link">了解更多 →</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Event Recommendations -->
    <section class="product-zone">
        <div class="container">
            <h2 class="section-title">活动推荐</h2>
            <div class="product-grid">
                <div class="product-card">
                    <div class="product-image">Summer Festival</div>
                    <div class="product-content">
                        <h3 class="product-title">夏日音乐节</h3>
                        <p class="product-description">参与全球顶级音乐节，感受音乐的魅力</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Tokyo Olympics</div>
                    <div class="product-content">
                        <h3 class="product-title">东京奥运会</h3>
                        <p class="product-description">见证体育盛事，感受奥运精神</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Business Conference</div>
                    <div class="product-content">
                        <h3 class="product-title">商务会议</h3>
                        <p class="product-description">参加国际商务会议，拓展商业网络</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
                <div class="product-card">
                    <div class="product-image">Cultural Exchange</div>
                    <div class="product-content">
                        <h3 class="product-title">文化交流</h3>
                        <p class="product-description">体验不同文化，开拓国际视野</p>
                        <a href="#" class="product-link">了解更多 →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>客户服务</h3>
                <ul>
                    <li><a href="#">在线客服</a></li>
                    <li><a href="#">常见问题</a></li>
                    <li><a href="#">联系我们</a></li>
                    <li><a href="#">投诉建议</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>航班信息</h3>
                <ul>
                    <li><a href="#">航班查询</a></li>
                    <li><a href="#">航班动态</a></li>
                    <li><a href="#">机场信息</a></li>
                    <li><a href="#">行李规定</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>服务与产品</h3>
                <ul>
                    <li><a href="#">会员服务</a></li>
                    <li><a href="#">贵宾厅</a></li>
                    <li><a href="#">特殊服务</a></li>
                    <li><a href="#">货运服务</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>关于我们</h3>
                <ul>
                    <li><a href="#">公司简介</a></li>
                    <li><a href="#">新闻中心</a></li>
                    <li><a href="#">招聘信息</a></li>
                    <li><a href="#">投资者关系</a></li>
                </ul>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 航空公司. 保留所有权利. | 隐私政策 | 使用条款</p>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const navMenu = document.getElementById('navMenu');

        mobileMenuBtn.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });

        // Booking tabs functionality
        const bookingTabs = document.querySelectorAll('.booking-tab');
        bookingTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                bookingTabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
            });
        });

        // Set default dates
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const nextWeek = new Date(today);
        nextWeek.setDate(nextWeek.getDate() + 7);

        document.getElementById('departure').value = tomorrow.toISOString().split('T')[0];
        document.getElementById('return').value = nextWeek.toISOString().split('T')[0];
    </script>
</body>
</html>
